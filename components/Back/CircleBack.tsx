import {Paths} from "@/enum/Paths";
import {useRouter} from "expo-router";
import {TouchableOpacity} from "react-native";
import {ArrowLeftIcon, Icon} from "../ui/icon";

interface CircleBackProps {
    /** Target route to navigate to. If not provided, uses router.back() */
    to?: Paths | string;
}

export const CircleBack = ({to}: CircleBackProps) => {
    const router = useRouter();

    const handlePress = () => {
        if (to) {
            router.push(to as any);
        } else {
            router.back();
        }
    };

    return (
        <TouchableOpacity
            onPress={handlePress}
            className="rounded-full bg-primary-500 w-12 h-12 items-center justify-center px-4"
        >
            <Icon as={ArrowLeftIcon} size="lg" stroke={"white"} />
        </TouchableOpacity>
    );
};
