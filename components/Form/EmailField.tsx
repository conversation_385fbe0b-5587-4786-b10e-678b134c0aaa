import {Input, InputField, InputSlot} from "@/components/ui/input";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import React from "react";
import {styles} from "./styles";
import {FormControl, FormControlLabelText} from "@/components/ui/form-control";

import {useFormContext, useController} from "react-hook-form";
import {FormFieldError} from "@/components/Form/FieldError";

type Props = {
    name?: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
};

export const EmailField = ({name = "email", label = "Email", placeholder = "Enter email", required = true}: Props) => {
    const {control} = useFormContext();
    const {field, fieldState} = useController({control, name});

    return (
        <FormControl isInvalid={!!fieldState.error} className={styles.classes.container}>
            <FormControlLabelText className={styles.classes.label} size="sm">
                {label}
                {required ? "*" : ""}
            </FormControlLabelText>

            <Input size="xl" className={styles.classes.input} isRequired={required}>
                <InputField
                    type="text"
                    placeholder={placeholder}
                    value={field.value ?? ""}
                    onChangeText={field.onChange}
                    onBlur={field.onBlur}
                    keyboardType="email-address"
                    textContentType="emailAddress"
                    autoCapitalize="none"
                    style={{fontSize: 16}}
                />
                <InputSlot className={styles.classes.inputSlot}>
                    <FontAwesome name="envelope-o" size={16} color="#747474" /*TODO: color from theme?*/ />
                </InputSlot>
            </Input>

            {fieldState.error ? <FormFieldError message={fieldState.error.message} /> : null}
        </FormControl>
    );
};
