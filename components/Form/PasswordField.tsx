import {Input, InputField, InputIcon, InputSlot} from "@/components/ui/input";
import React from "react";
import {usePasswordVisibility} from "@/hooks/usePasswordVisibility";
import {EyeIcon, EyeOffIcon} from "@/components/ui/icon";
import {styles} from "./styles";
import {
    FormControl,
    FormControlHelper,
    FormControlHelperText,
    FormControlLabelText,
} from "@/components/ui/form-control";
import {useController, useFormContext} from "react-hook-form";
import {FormFieldError} from "@/components/Form/FieldError";

type Props = {
    name?: string;
    label?: string;
    placeholder?: string;
    required?: boolean;
    helperText?: string;
};

export function PasswordField({
    name = "password",
    label = "Password",
    placeholder = "Password",
    helperText,
    required = true,
}: Props) {
    const {control} = useFormContext();
    const {field, fieldState} = useController({control, name});
    const {isVisible, toggle} = usePasswordVisibility();

    return (
        <FormControl isInvalid={!!fieldState.error} className={styles.classes.container}>
            <FormControlLabelText className={styles.classes.label} size="sm">
                {label}
                {required ? "*" : ""}
            </FormControlLabelText>

            <Input size="xl" className={styles.classes.input} isRequired={required}>
                <InputField
                    type={isVisible ? "text" : "password"}
                    placeholder={placeholder}
                    value={(field.value as string) ?? ""}
                    onChangeText={field.onChange}
                    onBlur={field.onBlur}
                    secureTextEntry={!isVisible}
                    textContentType="password"
                    style={{fontSize: 16}}
                />
                <InputSlot className={styles.classes.inputSlot} onPress={toggle}>
                    <InputIcon as={isVisible ? EyeIcon : EyeOffIcon} size="sm" />
                </InputSlot>
            </Input>

            {helperText ? (
                <FormControlHelper>
                    <FormControlHelperText>{helperText}</FormControlHelperText>
                </FormControlHelper>
            ) : null}

            {fieldState.error ? <FormFieldError message={fieldState.error.message} /> : null}
        </FormControl>
    );
}
