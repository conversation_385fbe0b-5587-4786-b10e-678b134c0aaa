import {config} from "@/components/ui/gluestack-ui-provider/config";
import {useColorScheme as useSystemColorScheme} from "@/hooks/useColorScheme";
import {useColorScheme as useNativewindColorScheme} from "nativewind";

// Zwraca obiekt zmiennych dla aktualnego motywu (light/dark)
export const useThemeColor = (name: string) => {
    const {colorScheme} = useNativewindColorScheme(); // "light" | "dark" | "system" | undefined (na 1. renderze)
    const systemScheme = useSystemColorScheme(); // systemowe "light" | "dark"

    // Fallback: jeżeli "system" lub undefined na pierwszym renderze, użyj systemowego motywu
    const effectiveScheme =
        colorScheme === "light" || colorScheme === "dark" ? colorScheme : systemScheme === "dark" ? "dark" : "light";

    return `rgb(${config[effectiveScheme][name]})`;
};
