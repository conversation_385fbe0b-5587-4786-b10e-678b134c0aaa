import {StyleSheet, Text, View} from "react-native";
import {useRouter} from "expo-router";
import {Paths} from "@/enum/Paths";
import {Button, ButtonText} from "@/components/ui/button";

export default function ExploreScreen() {
    const router = useRouter();

    const handleRedirectToLogin = () => {
        router.push(Paths.LOGIN);
    };

    return (
        <View style={styles.container}>
            <Button onPress={handleRedirectToLogin}>
                <ButtonText>Login</ButtonText>
            </Button>
            <Text>Explore</Text>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        alignItems: "center",
        justifyContent: "center",
    },
});
