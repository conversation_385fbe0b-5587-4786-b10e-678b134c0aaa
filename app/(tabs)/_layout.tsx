import {Tabs} from "expo-router";
import {Platform} from "react-native";

import {AccountIcon, CompasIcon, Icon, OrganizerIcon} from "@/components/ui/icon";
import TabBarBackground from "@/components/ui/TabBarBackground";
import {useThemeColor} from "@/hooks/useThemeColor";

export default function TabLayout() {
    const activeTint = useThemeColor("--color-primary-500");
    const inactiveTint = useThemeColor("--color-primary-300");

    return (
        <Tabs
            initialRouteName="explore"
            screenOptions={{
                headerShown: false,
                tabBarBackground: TabBarBackground,
                tabBarActiveTintColor: activeTint,
                tabBarInactiveTintColor: inactiveTint,
                tabBarStyle: Platform.select({
                    ios: {
                        // Use a transparent background on iOS to show the blur effect
                        position: "absolute",
                    },
                    default: {},
                }),
            }}
        >
            <Tabs.Screen
                name="explore"
                options={{
                    title: "Explore",
                    tabBarIcon: ({color}) => (
                        <Icon as={CompasIcon} size="xl" fill={color} stroke={color} color={color} />
                    ),
                }}
            />
            <Tabs.Screen
                name="organizer"
                options={{
                    title: "Organizer",
                    tabBarIcon: ({color}) => <Icon as={OrganizerIcon} size="xl" fill={color} stroke={color} />,
                }}
            />
            <Tabs.Screen
                name="account"
                options={{
                    title: "Account",
                    href: "/account",
                    tabBarIcon: ({color}) => <Icon as={AccountIcon} size="xl" fill={color} stroke={color} />,
                }}
            />
        </Tabs>
    );
}
