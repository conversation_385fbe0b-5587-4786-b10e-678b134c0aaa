import React from "react";
import {View} from "react-native";

import {
    Actionsheet,
    ActionsheetContent,
    ActionsheetItem,
    ActionsheetDragIndicatorWrapper,
    ActionsheetBackdrop,
} from "@/components/ui/actionsheet";
import {GestureHandlerRootView} from "react-native-gesture-handler";
import {Button} from "@/components/ui/button";
import AntDesign from "@expo/vector-icons/AntDesign";
import {useRouter} from "expo-router";
import {Logo} from "@/components/CustomIcons/Logo";
import {LoginForm, LoginFormValues} from "@/components/Form/LoginForm";

export default function LoginScreen() {
    const router = useRouter();

    const handleClose = () => {
        console.log("Close pressed");
        router.back();
    };

    const handleGoBack = () => {
        console.log("Back arrow pressed");
        router.back();
    };

    const handleForgotPasswordPress = () => {
        console.log("Forgot password pressed");
    };

    const handleFormSubmit = (values: LoginFormValues) => {
        console.log("Form submitted with values: ", values);
    };

    return (
        <GestureHandlerRootView>
            <Actionsheet isOpen={true} onClose={handleClose}>
                <ActionsheetBackdrop />
                <ActionsheetContent>
                    <ActionsheetDragIndicatorWrapper className="h-[80px] relative">
                        <View className="justify-between w-full flex-row">
                            <AntDesign onPress={handleGoBack} name="arrowleft" size={24} color="black" />
                            <View className="absolute top-[24px] self-center align-middle justify-center items-center w-full h-full">
                                <Logo />
                            </View>
                            <Button onPress={handleClose} variant="link">
                                <AntDesign name="close" size={24} color="black" />
                            </Button>
                        </View>
                    </ActionsheetDragIndicatorWrapper>
                    <ActionsheetItem className="p-0">
                        <LoginForm onSubmit={handleFormSubmit} onForgotPasswordPress={handleForgotPasswordPress} />
                    </ActionsheetItem>
                </ActionsheetContent>
            </Actionsheet>
        </GestureHandlerRootView>
    );
}
