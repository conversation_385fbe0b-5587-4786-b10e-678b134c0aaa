import {useFonts} from "expo-font";
import {Stack} from "expo-router";
import "react-native-reanimated";

import {GluestackUIProvider} from "@/components/ui/gluestack-ui-provider";
import "@/global.css";
import {SafeAreaProvider} from "react-native-safe-area-context";
import {NativeStackNavigationOptions} from "@react-navigation/native-stack";

const authStackScreenOptions: NativeStackNavigationOptions = {
    headerShown: false,
    presentation: "transparentModal",
    animation: "fade",
    contentStyle: {backgroundColor: "transparent"},
    gestureEnabled: true,
};

export default function RootLayout() {
    const [loaded] = useFonts({
        SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
    });

    if (!loaded) {
        // Async font loading only occurs in development.
        return null;
    }

    return (
        <GluestackUIProvider mode="light">
            <SafeAreaProvider>
                <Stack>
                    <Stack.Screen name="(tabs)" options={{headerShown: false}} />
                    <Stack.Screen name="(auth)" options={authStackScreenOptions} />
                    <Stack.Screen name="+not-found" />
                </Stack>
            </SafeAreaProvider>
        </GluestackUIProvider>
    );
}
